import { useState, useEffect, useCallback } from 'react';
import { RealWorldDisaster } from '../types';

export interface Alert {
  id: string;
  disaster: RealWorldDisaster;
  severity: 'low' | 'medium' | 'high' | 'critical';
  acknowledged: boolean;
  createdAt: Date;
}

export interface UseRealTimeAlertsOptions {
  refreshInterval?: number;
  severityFilter?: string[];
  maxAlerts?: number;
}

export interface UseRealTimeAlertsReturn {
  alerts: Alert[];
  unreadCount: number;
  criticalCount: number;
  acknowledgeAlert: (alertId: string) => void;
  dismissAlert: (alertId: string) => void;
  isOnline: boolean;
  lastUpdate: Date | null;
}

export const useRealTimeAlerts = (
  options: UseRealTimeAlertsOptions = {}
): UseRealTimeAlertsReturn => {
  const {
    refreshInterval = 30000,
    severityFilter = ['critical', 'high'],
    maxAlerts = 10
  } = options;

  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Mock data for demonstration - replace with actual API calls
  const fetchAlerts = useCallback(async () => {
    try {
      // This would be replaced with actual API call
      const mockAlerts: Alert[] = [
        {
          id: '1',
          disaster: {
            id: 'eq-1',
            title: 'M 6.2 Earthquake - 15km NE of Ridgecrest, CA',
            description: 'A magnitude 6.2 earthquake occurred 15km northeast of Ridgecrest, California. Shaking was felt across Southern California.',
            location: {
              coordinates: { lat: 35.766, lng: -117.504 },
              place: 'Ridgecrest, CA'
            },
            disasterType: 'earthquake',
            severity: 'high',
            magnitude: 6.2,
            time: new Date(Date.now() - 300000), // 5 minutes ago
            updatedAt: new Date(),
            source: 'USGS',
            url: 'https://earthquake.usgs.gov/earthquakes/eventpage/ci39126079',
            alertLevel: 'orange',
            depth: 8.1,
            significance: 650
          },
          severity: 'high',
          acknowledged: false,
          createdAt: new Date(Date.now() - 300000)
        }
      ];

      const filteredAlerts = mockAlerts
        .filter(alert => severityFilter.includes(alert.severity))
        .slice(0, maxAlerts);

      setAlerts(filteredAlerts);
      setLastUpdate(new Date());
      setIsOnline(true);
    } catch (error) {
      console.error('Failed to fetch alerts:', error);
      setIsOnline(false);
    }
  }, [severityFilter, maxAlerts]);

  const acknowledgeAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId 
        ? { ...alert, acknowledged: true }
        : alert
    ));
  }, []);

  const dismissAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  }, []);

  // Set up polling for real-time updates
  useEffect(() => {
    fetchAlerts();
    const interval = setInterval(fetchAlerts, refreshInterval);
    return () => clearInterval(interval);
  }, [fetchAlerts, refreshInterval]);

  // Listen for online/offline events
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const unreadCount = alerts.filter(alert => !alert.acknowledged).length;
  const criticalCount = alerts.filter(alert => alert.severity === 'critical').length;

  return {
    alerts,
    unreadCount,
    criticalCount,
    acknowledgeAlert,
    dismissAlert,
    isOnline,
    lastUpdate
  };
};