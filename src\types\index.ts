// User types
export interface User {
  userId: string;
  name: string;
  email: string;
  photoUrl?: string;
  roles: string[];
}

// Authentication request types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignupRequest {
  fullName: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

export interface GoogleLoginRequest {
  idToken: string;
  deviceInfo?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// Authentication response types
export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
  user: User;
}

// API response wrapper
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Error types
export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
  statusCode: number;
}

// Disaster Report types
export interface AssistanceLogEntry {
  id: string;
  providerName: string;
  description: string;
  createdAt: Date;
  endorsed: boolean;
}

export interface Report {
  id: string;
  title: string;
  description: string;
  location: {
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  disasterType: 'flood' | 'fire' | 'earthquake' | 'storm' | 'drought' | 'landslide' | 'tsunami' | 'volcano' | 'hurricane' | 'tornado' | 'wildfire' | 'chemical_spill' | 'nuclear_incident' | 'industrial_accident' | 'structural_failure' | 'transportation_accident' | 'cyber_attack' | 'power_outage' | 'infrastructure_failure' | 'other';
  disasterDetail: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'verified' | 'resolved';
  createdAt: Date;
  updatedAt: Date;
  reporterName: string;
  photos: string[];
  verified: boolean;
  assistanceNeeded: string[];
  assistanceDescription: string;
  assistanceLog: AssistanceLogEntry[];
}

// Statistics types
export interface Statistics {
  reportsSubmitted: number;
  livesHelped: number;
  verifiedReports: number;
  averageResponseTime: string;
}

// Feature types
export interface Feature {
  id: string;
  number: string;
  title: string;
  description: string;
  icon: any; // Lucide icon component
  color: string;
  bgColor: string;
}

// Partner types
export interface Partner {
  id: string;
  name: string;
  logo: string;
  website?: string;
}

// Real-world disaster data interfaces
export interface RealWorldDisaster {
  id: string;
  title: string;
  description: string;
  location: {
    coordinates: { lat: number; lng: number };
    place: string;
  };
  disasterType: 'earthquake' | 'flood' | 'hurricane' | 'wildfire' | 'storm' | 'tsunami' | 'volcano' | 'drought' | 'landslide' | 'severeStorms' | 'dustHaze' | 'snowIce' | 'tempExtremes' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  magnitude?: number;
  time: Date;
  updatedAt: Date;
  source: string;
  url?: string;
  alertLevel?: 'green' | 'yellow' | 'orange' | 'red';
  depth?: number;
  felt?: number;
  tsunami?: boolean;
  significance?: number;
}

export interface USGSEarthquake {
  type: 'Feature';
  properties: {
    mag: number;
    place: string;
    time: number;
    updated: number;
    tz?: number;
    url: string;
    detail: string;
    felt?: number;
    cdi?: number;
    mmi?: number;
    alert?: 'green' | 'yellow' | 'orange' | 'red';
    status: string;
    tsunami: number;
    sig: number;
    net: string;
    code: string;
    ids: string;
    sources: string;
    types: string;
    nst?: number;
    dmin?: number;
    rms?: number;
    gap?: number;
    magType: string;
    type: string;
    title: string;
  };
  geometry: {
    type: 'Point';
    coordinates: [number, number, number]; // [longitude, latitude, depth]
  };
  id: string;
}

export interface USGSEarthquakeResponse {
  type: 'FeatureCollection';
  metadata: {
    generated: number;
    url: string;
    title: string;
    status: number;
    api: string;
    count: number;
  };
  features: USGSEarthquake[];
  bbox: [number, number, number, number, number, number];
}

// NASA EONET API Types
export interface EONETEvent {
  id: string;
  title: string;
  description?: string;
  link: string;
  closed?: string | null;
  categories: Array<{
    id: string;
    title: string;
  }>;
  sources: Array<{
    id: string;
    url: string;
  }>;
  geometry: Array<{
    magnitudeValue?: number;
    magnitudeUnit?: string;
    date: string;
    type: 'Point' | 'Polygon';
    coordinates: number[] | number[][];
  }>;
}

export interface EONETResponse {
  title: string;
  description: string;
  link: string;
  events: EONETEvent[];
}

// OpenWeather API Types
export interface OpenWeatherAlert {
  sender_name: string;
  event: string;
  start: number;
  end: number;
  description: string;
  tags: string[];
}

export interface OpenWeatherCurrent {
  dt: number;
  temp: number;
  feels_like: number;
  pressure: number;
  humidity: number;
  uvi: number;
  clouds: number;
  visibility: number;
  wind_speed: number;
  wind_deg: number;
  weather: Array<{
    id: number;
    main: string;
    description: string;
    icon: string;
  }>;
}

export interface OpenWeatherResponse {
  lat: number;
  lon: number;
  timezone: string;
  timezone_offset: number;
  current: OpenWeatherCurrent;
  alerts?: OpenWeatherAlert[];
}

// GDACS API Types
export interface GDACSEvent {
  eventid: string;
  eventtype: string;
  alertlevel: string;
  alertscore: number;
  episodeid: string;
  eventname: string;
  eventdate: string;
  fromdate: string;
  todate: string;
  subject: string;
  htmldescription: string;
  country: string;
  iso3: string;
  glide: string;
  latitude: number;
  longitude: number;
  severity: string;
  severitydata: {
    severityvalue: number;
    severityunit: string;
    severitytext: string;
  };
  population: {
    value: number;
    unit: string;
  };
  vulnerability: {
    value: number;
    unit: string;
  };
}

export interface GDACSResponse {
  events: GDACSEvent[];
}

// Google types
declare global {
  interface Window {
    google: any;
  }
}