import React, { useState } from 'react';
import {
  AlertTriangle,
  X,
  ExternalLink,
  MapPin,
  Clock,
  Zap,
  Activity,
  Shield,
  ChevronDown,
  ChevronUp,
  Bell
} from 'lucide-react';
import { useRealTimeAlerts, Alert } from '../../hooks/useRealTimeAlerts';

interface AlertBannerProps {
  className?: string;
  showOnlyActive?: boolean;
  maxVisible?: number;
}

const AlertBanner: React.FC<AlertBannerProps> = ({
  className = '',
  showOnlyActive = true,
  maxVisible = 3
}) => {
  const {
    alerts,
    unreadCount,
    criticalCount,
    acknowledgeAlert,
    dismissAlert,
    isOnline,
    lastUpdate
  } = useRealTimeAlerts({
    refreshInterval: 30000,
    severityFilter: ['critical', 'high'],
    maxAlerts: 10
  });

  const [isExpanded, setIsExpanded] = useState(false);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500 border-red-600 text-white';
      case 'high': return 'bg-orange-500 border-orange-600 text-white';
      case 'medium': return 'bg-yellow-500 border-yellow-600 text-white';
      default: return 'bg-blue-500 border-blue-600 text-white';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return AlertTriangle;
      case 'high': return Zap;
      case 'medium': return Activity;
      default: return Shield;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  const activeAlerts = showOnlyActive ? alerts.filter((alert: Alert) => !alert.acknowledged) : alerts;
  const visibleAlerts = isExpanded ? activeAlerts : activeAlerts.slice(0, maxVisible);

  if (activeAlerts.length === 0) {
    return null;
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Main Alert Banner */}
      <div className={`rounded-xl shadow-lg border-2 backdrop-blur-sm transition-all duration-300 ${
        criticalCount > 0
          ? 'bg-red-50/90 border-red-200 animate-pulse'
          : 'bg-orange-50/90 border-orange-200'
      }`}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${
                criticalCount > 0 ? 'bg-red-500 text-white' : 'bg-orange-500 text-white'
              }`}>
                <Bell size={20} />
              </div>
              <div>
                <h3 className="font-bold text-gray-900">
                  {criticalCount > 0 ? '🚨 CRITICAL EMERGENCY ALERTS' : '⚠️ Live Emergency Alerts'}
                </h3>
                <p className="text-sm text-gray-600">
                  {unreadCount} active alert{unreadCount !== 1 ? 's' : ''} • 
                  Last updated: {lastUpdate ? formatTimeAgo(lastUpdate) : 'Never'} •
                  <span className={`ml-1 ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
                    {isOnline ? 'Online' : 'Offline'}
                  </span>
                </p>
              </div>
            </div>
            
            {activeAlerts.length > maxVisible && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center space-x-2 px-3 py-2 bg-white/80 rounded-lg hover:bg-white transition-colors text-sm font-medium text-gray-700"
              >
                <span>{isExpanded ? 'Show Less' : `Show All (${activeAlerts.length})`}</span>
                {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </button>
            )}
          </div>
        </div>

        {/* Alert List */}
        <div className="divide-y divide-gray-200/50">
          {visibleAlerts.map((alert: Alert) => {
            const SeverityIcon = getSeverityIcon(alert.severity);
            
            return (
              <div
                key={alert.id}
                className="px-6 py-4 hover:bg-white/50 transition-colors"
              >
                <div className="flex items-start space-x-4">
                  {/* Severity Indicator */}
                  <div className={`p-2 rounded-lg ${getSeverityColor(alert.severity)} flex-shrink-0`}>
                    <SeverityIcon size={18} />
                  </div>
                  
                  {/* Alert Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-semibold text-gray-900 text-base">
                        {alert.disaster.title}
                      </h4>
                      <div className="flex items-center space-x-2 ml-4">
                        {alert.disaster.url && (
                          <a
                            href={alert.disaster.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 p-1"
                            title="View details"
                          >
                            <ExternalLink size={16} />
                          </a>
                        )}
                        <button
                          onClick={() => dismissAlert(alert.id)}
                          className="text-gray-400 hover:text-gray-600 p-1"
                          title="Dismiss alert"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    </div>
                    
                    {/* Location and Time */}
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                      <div className="flex items-center space-x-1">
                        <MapPin size={14} />
                        <span className="truncate max-w-xs">{alert.disaster.location.place}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock size={14} />
                        <span>{formatTimeAgo(alert.disaster.time)}</span>
                      </div>
                      {alert.disaster.magnitude && (
                        <div className="flex items-center space-x-1">
                          <span className="font-medium">M{alert.disaster.magnitude.toFixed(1)}</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Description */}
                    <p className="text-sm text-gray-700 mb-3 line-clamp-2">
                      {alert.disaster.description}
                    </p>
                    
                    {/* Actions */}
                    <div className="flex items-center space-x-3">
                      {!alert.acknowledged && (
                        <button
                          onClick={() => acknowledgeAlert(alert.id)}
                          className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors"
                        >
                          Acknowledge
                        </button>
                      )}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        alert.severity === 'critical' 
                          ? 'bg-red-100 text-red-800'
                          : alert.severity === 'high'
                          ? 'bg-orange-100 text-orange-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {alert.severity.toUpperCase()}
                      </span>
                      {alert.acknowledged && (
                        <span className="text-xs text-green-600 font-medium">
                          ✓ Acknowledged
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        {activeAlerts.length > 0 && (
          <div className="px-6 py-3 bg-gray-50/80 border-t border-gray-200/50 rounded-b-xl">
            <div className="flex items-center justify-between text-sm">
              <p className="text-gray-600">
                Data from USGS Earthquake Hazards Program and other verified sources
              </p>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-gray-500 text-xs">
                  {isOnline ? 'Live monitoring active' : 'Connection lost'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertBanner;