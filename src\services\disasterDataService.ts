
import { RealWorldDisaster, USGSEarthquakeResponse, USGSEarthquake, EONETResponse, EONETEvent, OpenWeatherResponse, GDACSResponse } from '../types';
import { mockReports } from '../data/mockData';
import { requestManager } from '../utils/requestManager';

// API endpoints
const USGS_BASE_URL = 'https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary';
const NASA_EONET_BASE_URL = 'https://eonet.gsfc.nasa.gov/api/v3';
const OPENWEATHER_BASE_URL = 'https://api.openweathermap.org/data/2.5';
const GDACS_BASE_URL = 'https://www.gdacs.org/gdacsapi/api';

// Available USGS feeds
export const USGS_FEEDS = {
  SIGNIFICANT_DAY: `${USGS_BASE_URL}/significant_day.geo<PERSON><PERSON>`,
  SIGNIFICANT_WEEK: `${USGS_BASE_URL}/significant_week.geojson`,
  M4_5_DAY: `${USGS_BASE_URL}/4.5_day.geojson`,
  M4_5_WEEK: `${USGS_BASE_URL}/4.5_week.geojson`,
  M2_5_DAY: `${USGS_BASE_URL}/2.5_day.geojson`,
  M2_5_WEEK: `${USGS_BASE_URL}/2.5_week.geojson`,
  ALL_DAY: `${USGS_BASE_URL}/all_day.geojson`,
  ALL_WEEK: `${USGS_BASE_URL}/all_week.geojson`,
} as const;

// Severity mapping based on earthquake magnitude
const getMagnitudeSeverity = (magnitude: number): 'low' | 'medium' | 'high' | 'critical' => {
  if (magnitude >= 7.0) return 'critical';
  if (magnitude >= 6.0) return 'high';
  if (magnitude >= 4.5) return 'medium';
  return 'low';
};

// Alert level to severity mapping
const getAlertSeverity = (alert?: string): 'low' | 'medium' | 'high' | 'critical' => {
  switch (alert) {
    case 'red': return 'critical';
    case 'orange': return 'high';
    case 'yellow': return 'medium';
    case 'green':
    default: return 'low';
  }
};

// Helper function to get the higher severity level
const getHigherSeverity = (severity1: 'low' | 'medium' | 'high' | 'critical', severity2: 'low' | 'medium' | 'high' | 'critical'): 'low' | 'medium' | 'high' | 'critical' => {
  const severityOrder = { 'low': 1, 'medium': 2, 'high': 3, 'critical': 4 };
  return severityOrder[severity1] >= severityOrder[severity2] ? severity1 : severity2;
};

// Convert USGS earthquake data to our standard format
const convertUSGSToDisaster = (earthquake: USGSEarthquake): RealWorldDisaster => {
  const { properties, geometry, id } = earthquake;
  const [longitude, latitude, depth] = geometry.coordinates;

  // Calculate severity based on both magnitude and alert level
  const magnitudeSeverity = getMagnitudeSeverity(properties.mag || 0);
  const alertSeverity = getAlertSeverity(properties.alert);
  const finalSeverity = getHigherSeverity(magnitudeSeverity, alertSeverity);



  return {
    id: id || `earthquake-${Date.now()}`,
    title: properties.title || 'Earthquake Event',
    description: `${(properties.magType || 'M').toUpperCase()} ${properties.mag || 'Unknown'} earthquake ${properties.place || 'Unknown location'}. ${
      properties.felt ? `Felt by ${properties.felt} people. ` : ''
    }${properties.tsunami ? 'Tsunami warning issued. ' : ''}`,
    location: {
      coordinates: { lat: latitude || 0, lng: longitude || 0 },
      place: properties.place || 'Unknown location',
    },
    disasterType: 'earthquake',
    severity: finalSeverity,
    magnitude: properties.mag || 0,
    time: new Date(properties.time || Date.now()),
    updatedAt: new Date(properties.updated || Date.now()),
    source: 'USGS',
    url: properties.url || '',
    alertLevel: properties.alert || 'green',
    depth: depth || 0,
    felt: properties.felt || 0,
    tsunami: properties.tsunami === 1,
    significance: properties.sig || 0,
  };
};

// Convert NASA EONET event to our standard format
const convertEONETToDisaster = (event: EONETEvent): RealWorldDisaster => {
  // Get the most recent geometry point
  const latestGeometry = event.geometry[event.geometry.length - 1];
  const coordinates = latestGeometry.type === 'Point'
    ? latestGeometry.coordinates as [number, number]
    : (latestGeometry.coordinates as number[][])[0] as [number, number];

  // Map EONET categories to our disaster types
  const categoryMapping: Record<string, RealWorldDisaster['disasterType']> = {
    'drought': 'drought',
    'dustHaze': 'dustHaze',
    'earthquakes': 'earthquake',
    'floods': 'flood',
    'landslides': 'landslide',
    'severeStorms': 'severeStorms',
    'snow': 'snowIce',
    'tempExtremes': 'tempExtremes',
    'volcanoes': 'volcano',
    'wildfires': 'wildfire',
  };

  const primaryCategory = event.categories[0];
  const disasterType = categoryMapping[primaryCategory?.id] || 'other';

  // Determine severity based on magnitude or category
  let severity: 'low' | 'medium' | 'high' | 'critical' = 'medium';
  if (latestGeometry.magnitudeValue) {
    if (latestGeometry.magnitudeValue >= 7) severity = 'critical';
    else if (latestGeometry.magnitudeValue >= 5) severity = 'high';
    else if (latestGeometry.magnitudeValue >= 3) severity = 'medium';
    else severity = 'low';
  }

  return {
    id: event.id,
    title: event.title,
    description: event.description || `${primaryCategory?.title || 'Natural event'} detected by NASA EONET.`,
    location: {
      coordinates: { lat: coordinates[1], lng: coordinates[0] },
      place: event.title.split(' - ')[1] || 'Unknown location',
    },
    disasterType,
    severity,
    magnitude: latestGeometry.magnitudeValue,
    time: new Date(latestGeometry.date),
    updatedAt: new Date(),
    source: 'NASA EONET',
    url: event.link,
  };
};

// Convert OpenWeather alert to our standard format
const convertOpenWeatherToDisaster = (alert: any, location: { lat: number; lng: number; name: string }): RealWorldDisaster => {
  // Map weather event types to our disaster types
  const eventMapping: Record<string, RealWorldDisaster['disasterType']> = {
    'Thunderstorm': 'severeStorms',
    'Tornado': 'severeStorms',
    'Hurricane': 'hurricane',
    'Flood': 'flood',
    'Blizzard': 'snowIce',
    'Ice Storm': 'snowIce',
    'Heat': 'tempExtremes',
    'Cold': 'tempExtremes',
    'Wind': 'severeStorms',
    'Rain': 'severeStorms',
    'Snow': 'snowIce',
    'Fog': 'other',
  };

  const eventType = alert.event || 'Weather Alert';
  const disasterType = Object.keys(eventMapping).find(key =>
    eventType.toLowerCase().includes(key.toLowerCase())
  ) ? eventMapping[Object.keys(eventMapping).find(key =>
    eventType.toLowerCase().includes(key.toLowerCase())
  )!] : 'storm';

  // Determine severity based on alert tags
  let severity: 'low' | 'medium' | 'high' | 'critical' = 'medium';
  if (alert.tags?.some((tag: string) => tag.toLowerCase().includes('extreme'))) {
    severity = 'critical';
  } else if (alert.tags?.some((tag: string) => tag.toLowerCase().includes('severe'))) {
    severity = 'high';
  } else if (alert.tags?.some((tag: string) => tag.toLowerCase().includes('moderate'))) {
    severity = 'medium';
  } else {
    severity = 'low';
  }

  return {
    id: `weather-${alert.start}-${location.lat}-${location.lng}`,
    title: `${eventType} - ${location.name}`,
    description: alert.description || `Weather alert issued by ${alert.sender_name || 'Weather Service'}`,
    location: {
      coordinates: { lat: location.lat, lng: location.lng },
      place: location.name,
    },
    disasterType,
    severity,
    time: new Date(alert.start * 1000),
    updatedAt: new Date(),
    source: 'OpenWeather',
    url: '',
  };
};

// Convert GDACS event to our standard format
const convertGDACSToDisaster = (event: GDACSEvent): RealWorldDisaster => {
  // Map GDACS event types to our disaster types
  const eventMapping: Record<string, RealWorldDisaster['disasterType']> = {
    'EQ': 'earthquake',
    'FL': 'flood',
    'TC': 'hurricane',
    'VO': 'volcano',
    'WF': 'wildfire',
    'DR': 'drought',
    'LS': 'landslide',
    'ST': 'severeStorms',
  };

  const disasterType = eventMapping[event.eventtype] || 'other';

  // Map GDACS alert levels to our severity
  const severityMapping: Record<string, 'low' | 'medium' | 'high' | 'critical'> = {
    'Green': 'low',
    'Orange': 'medium',
    'Red': 'high',
  };

  const severity = severityMapping[event.alertlevel] || 'medium';

  return {
    id: event.eventid,
    title: event.eventname || `${event.eventtype} Event`,
    description: event.htmldescription?.replace(/<[^>]*>/g, '') || event.subject || 'Global disaster alert',
    location: {
      coordinates: { lat: event.latitude, lng: event.longitude },
      place: event.country || 'Unknown location',
    },
    disasterType,
    severity,
    magnitude: event.severitydata?.severityvalue,
    time: new Date(event.eventdate),
    updatedAt: new Date(),
    source: 'GDACS',
    url: `https://www.gdacs.org/report.aspx?eventtype=${event.eventtype}&eventid=${event.eventid}`,
    alertLevel: event.alertlevel?.toLowerCase() as 'green' | 'yellow' | 'orange' | 'red',
  };
};

// Disaster Data Service
export class DisasterDataService {
  private static instance: DisasterDataService;
  private cache: Map<string, { data: RealWorldDisaster[]; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes - longer cache to reduce API calls

  static getInstance(): DisasterDataService {
    if (!DisasterDataService.instance) {
      DisasterDataService.instance = new DisasterDataService();
    }
    return DisasterDataService.instance;
  }

  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.CACHE_DURATION;
  }

  /**
   * Generate fallback earthquake data from mock reports when API is unavailable
   */
  private generateFallbackEarthquakeData(): RealWorldDisaster[] {
    const earthquakeReports = mockReports.filter(report => report.disasterType === 'earthquake');

    return earthquakeReports.map(report => ({
      id: `fallback-${report.id}`,
      title: report.title,
      description: report.description,
      location: {
        coordinates: report.location.coordinates,
        place: report.location.address,
      },
      magnitude: 4.2 + Math.random() * 2.8, // Random magnitude between 4.2 and 7.0
      depth: 10 + Math.random() * 40, // Random depth between 10-50 km
      time: report.createdAt,
      severity: report.severity as 'low' | 'medium' | 'high' | 'critical',
      type: 'earthquake',
      source: 'Mock Data (Offline)',
      url: '#',
      alert: null,
      tsunami: false,
      felt: Math.floor(Math.random() * 100),
      significance: Math.floor(Math.random() * 1000),
    }));
  }

  // Track ongoing requests to prevent duplicate calls
  private ongoingRequests: Map<string, Promise<RealWorldDisaster[]>> = new Map();

  /**
   * Fetch earthquake data from USGS
   */
  async fetchUSGSEarthquakes(feedType: keyof typeof USGS_FEEDS = 'M2_5_DAY'): Promise<RealWorldDisaster[]> {
    const cacheKey = `usgs_${feedType}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && this.isCacheValid(cached.timestamp)) {
      return cached.data;
    }

    // Use global request manager to prevent duplicate calls
    return requestManager.executeRequest(cacheKey, () => 
      this.performUSGSRequest(feedType, cacheKey, cached)
    );
  }

  private async performUSGSRequest(
    feedType: keyof typeof USGS_FEEDS, 
    cacheKey: string, 
    cached?: { data: RealWorldDisaster[]; timestamp: number }
  ): Promise<RealWorldDisaster[]> {
    try {
      console.log(`Fetching earthquake data from: ${USGS_FEEDS[feedType]}`);
      
      // Create abort controller for better timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout
      
      const response = await fetch(USGS_FEEDS[feedType], {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DisasterResponse-App/1.0',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: USGSEarthquakeResponse = await response.json();
      console.log(`✅ Received ${data.features.length} earthquakes from USGS`);

      const disasters = data.features.map(convertUSGSToDisaster);
      
      // Cache the results
      this.cache.set(cacheKey, {
        data: disasters,
        timestamp: Date.now(),
      });

      return disasters;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn('⏱️ USGS earthquake data request timed out - using fallback data');
        } else if (error.message.includes('Failed to fetch') || error.message.includes('ERR_NAME_NOT_RESOLVED')) {
          console.warn('🌐 Network error: Unable to reach USGS earthquake service - using fallback data');
        } else {
          console.error('❌ Error fetching USGS earthquake data:', error.message);
        }
      } else {
        console.error('❌ Unknown error fetching USGS earthquake data:', error);
      }

      // Return cached data if available, even if expired
      if (cached) {
        console.warn('📦 Using expired cache data due to API error');
        return cached.data;
      }

      // If no cached data available, use fallback mock data
      console.warn('🔄 No cached data available, using fallback earthquake data from mock reports');
      const fallbackData = this.generateFallbackEarthquakeData();

      // Cache the fallback data for future use
      this.cache.set(cacheKey, {
        data: fallbackData,
        timestamp: Date.now(),
      });

      return fallbackData;
    }
  }

  /**
   * Fetch natural disaster events from NASA EONET
   */
  async fetchNASAEvents(days: number = 30): Promise<RealWorldDisaster[]> {
    const cacheKey = `nasa_eonet_${days}`;
    const cached = this.cache.get(cacheKey);

    if (cached && this.isCacheValid(cached.timestamp)) {
      return cached.data;
    }

    return requestManager.executeRequest(cacheKey, () =>
      this.performNASARequest(days, cacheKey, cached)
    );
  }

  private async performNASARequest(
    days: number,
    cacheKey: string,
    cached?: { data: RealWorldDisaster[]; timestamp: number }
  ): Promise<RealWorldDisaster[]> {
    try {
      const apiKey = import.meta.env.VITE_NASA_API_KEY || 'DEMO_KEY';
      const url = `${NASA_EONET_BASE_URL}/events?days=${days}&status=open&api_key=${apiKey}`;

      console.log(`Fetching NASA EONET data from: ${url}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'DisasterResponse-App/1.0',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: EONETResponse = await response.json();
      console.log(`✅ Received ${data.events.length} events from NASA EONET`);

      const disasters = data.events.map(convertEONETToDisaster);

      // Cache the results
      this.cache.set(cacheKey, {
        data: disasters,
        timestamp: Date.now(),
      });

      return disasters;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn('⏱️ NASA EONET request timed out - using fallback data');
        } else if (error.message.includes('Failed to fetch') || error.message.includes('ERR_NAME_NOT_RESOLVED')) {
          console.warn('🌐 Network error: Unable to reach NASA EONET service - using fallback data');
        } else {
          console.error('❌ Error fetching NASA EONET data:', error.message);
        }
      } else {
        console.error('❌ Unknown error fetching NASA EONET data:', error);
      }

      // Return cached data if available, even if expired
      if (cached) {
        console.warn('📦 Using expired cache data due to API error');
        return cached.data;
      }

      // Return empty array if no cached data available
      console.warn('🔄 No cached data available for NASA EONET');
      return [];
    }
  }

  /**
   * Fetch weather alerts from OpenWeather for major cities
   */
  async fetchWeatherAlerts(): Promise<RealWorldDisaster[]> {
    const cacheKey = 'openweather_alerts';
    const cached = this.cache.get(cacheKey);

    if (cached && this.isCacheValid(cached.timestamp)) {
      return cached.data;
    }

    return requestManager.executeRequest(cacheKey, () =>
      this.performOpenWeatherRequest(cacheKey, cached)
    );
  }

  private async performOpenWeatherRequest(
    cacheKey: string,
    cached?: { data: RealWorldDisaster[]; timestamp: number }
  ): Promise<RealWorldDisaster[]> {
    try {
      const apiKey = import.meta.env.VITE_OPENWEATHER_API_KEY;

      if (!apiKey || apiKey === 'your_openweather_api_key_here') {
        console.warn('⚠️ OpenWeather API key not configured');
        return cached?.data || [];
      }

      // Major cities to check for weather alerts (focusing on disaster-prone areas)
      const cities = [
        { name: 'Yangon, Myanmar', lat: 16.8661, lng: 96.1951 },
        { name: 'Mandalay, Myanmar', lat: 21.9588, lng: 96.0891 },
        { name: 'Bangkok, Thailand', lat: 13.7563, lng: 100.5018 },
        { name: 'Manila, Philippines', lat: 14.5995, lng: 120.9842 },
        { name: 'Jakarta, Indonesia', lat: -6.2088, lng: 106.8456 },
        { name: 'Tokyo, Japan', lat: 35.6762, lng: 139.6503 },
        { name: 'Mumbai, India', lat: 19.0760, lng: 72.8777 },
        { name: 'Dhaka, Bangladesh', lat: 23.8103, lng: 90.4125 },
      ];

      const allAlerts: RealWorldDisaster[] = [];

      for (const city of cities) {
        try {
          const url = `${OPENWEATHER_BASE_URL}/onecall?lat=${city.lat}&lon=${city.lng}&appid=${apiKey}&exclude=minutely,hourly,daily`;

          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000);

          const response = await fetch(url, {
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'DisasterResponse-App/1.0',
            },
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            const data: OpenWeatherResponse = await response.json();

            if (data.alerts && data.alerts.length > 0) {
              const cityAlerts = data.alerts.map(alert =>
                convertOpenWeatherToDisaster(alert, city)
              );
              allAlerts.push(...cityAlerts);
            }
          }
        } catch (error) {
          console.warn(`Failed to fetch weather data for ${city.name}:`, error);
        }

        // Add small delay between requests to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log(`✅ Received ${allAlerts.length} weather alerts from OpenWeather`);

      // Cache the results
      this.cache.set(cacheKey, {
        data: allAlerts,
        timestamp: Date.now(),
      });

      return allAlerts;
    } catch (error) {
      console.error('❌ Error fetching OpenWeather data:', error);

      // Return cached data if available, even if expired
      if (cached) {
        console.warn('📦 Using expired cache data due to API error');
        return cached.data;
      }

      return [];
    }
  }

  /**
   * Fetch global disaster alerts from GDACS
   */
  async fetchGDACSAlerts(): Promise<RealWorldDisaster[]> {
    const cacheKey = 'gdacs_alerts';
    const cached = this.cache.get(cacheKey);

    if (cached && this.isCacheValid(cached.timestamp)) {
      return cached.data;
    }

    return requestManager.executeRequest(cacheKey, () =>
      this.performGDACSRequest(cacheKey, cached)
    );
  }

  private async performGDACSRequest(
    cacheKey: string,
    cached?: { data: RealWorldDisaster[]; timestamp: number }
  ): Promise<RealWorldDisaster[]> {
    try {
      // GDACS API endpoint for recent events
      const url = `${GDACS_BASE_URL}/events/geteventlist/SEARCH`;

      console.log(`Fetching GDACS data from: ${url}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'User-Agent': 'DisasterResponse-App/1.0',
        },
        body: JSON.stringify({
          fromDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Last 7 days
          toDate: new Date().toISOString().split('T')[0],
          alertLevel: ['Orange', 'Red'], // Only significant alerts
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ Received ${data.length || 0} events from GDACS`);

      // GDACS API returns an array directly
      const disasters = Array.isArray(data) ? data.map(convertGDACSToDisaster) : [];

      // Cache the results
      this.cache.set(cacheKey, {
        data: disasters,
        timestamp: Date.now(),
      });

      return disasters;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn('⏱️ GDACS request timed out - using fallback data');
        } else if (error.message.includes('Failed to fetch') || error.message.includes('ERR_NAME_NOT_RESOLVED')) {
          console.warn('🌐 Network error: Unable to reach GDACS service - using fallback data');
        } else {
          console.error('❌ Error fetching GDACS data:', error.message);
        }
      } else {
        console.error('❌ Unknown error fetching GDACS data:', error);
      }

      // Return cached data if available, even if expired
      if (cached) {
        console.warn('📦 Using expired cache data due to API error');
        return cached.data;
      }

      // Return empty array if no cached data available
      console.warn('🔄 No cached data available for GDACS');
      return [];
    }
  }

  /**
   * Fetch significant earthquakes (magnitude 4.5+ or felt reports)
   */
  async fetchSignificantEarthquakes(): Promise<RealWorldDisaster[]> {
    try {
      const [significantDay, m45Day] = await Promise.allSettled([
        this.fetchUSGSEarthquakes('SIGNIFICANT_DAY'),
        this.fetchUSGSEarthquakes('M4_5_DAY'),
      ]);

      const disasters: RealWorldDisaster[] = [];
      
      if (significantDay.status === 'fulfilled') {
        disasters.push(...significantDay.value);
      }
      
      if (m45Day.status === 'fulfilled') {
        // Filter out duplicates by ID
        const existingIds = new Set(disasters.map(d => d.id));
        const newDisasters = m45Day.value.filter(d => !existingIds.has(d.id));
        disasters.push(...newDisasters);
      }

      // Sort by time (most recent first) and limit to 50 events
      return disasters
        .sort((a, b) => b.time.getTime() - a.time.getTime())
        .slice(0, 50);
    } catch (error) {
      console.error('Error fetching significant earthquakes:', error);
      return [];
    }
  }

  /**
   * Fetch all recent disasters from multiple sources
   */
  async fetchAllRecentDisasters(): Promise<RealWorldDisaster[]> {
    try {
      console.log('🌍 Fetching disaster data from multiple sources...');

      // Fetch from all available sources in parallel
      const [earthquakes, nasaEvents, weatherAlerts, gdacsAlerts] = await Promise.allSettled([
        this.fetchUSGSEarthquakes('M2_5_DAY'),
        this.fetchNASAEvents(30),
        this.fetchWeatherAlerts(),
        this.fetchGDACSAlerts(),
      ]);

      const allDisasters: RealWorldDisaster[] = [];

      // Collect successful results
      if (earthquakes.status === 'fulfilled') {
        allDisasters.push(...earthquakes.value);
        console.log(`✅ Added ${earthquakes.value.length} earthquakes from USGS`);
      } else {
        console.warn('❌ Failed to fetch USGS earthquakes:', earthquakes.reason);
      }

      if (nasaEvents.status === 'fulfilled') {
        allDisasters.push(...nasaEvents.value);
        console.log(`✅ Added ${nasaEvents.value.length} events from NASA EONET`);
      } else {
        console.warn('❌ Failed to fetch NASA EONET events:', nasaEvents.reason);
      }

      if (weatherAlerts.status === 'fulfilled') {
        allDisasters.push(...weatherAlerts.value);
        console.log(`✅ Added ${weatherAlerts.value.length} weather alerts from OpenWeather`);
      } else {
        console.warn('❌ Failed to fetch OpenWeather alerts:', weatherAlerts.reason);
      }

      if (gdacsAlerts.status === 'fulfilled') {
        allDisasters.push(...gdacsAlerts.value);
        console.log(`✅ Added ${gdacsAlerts.value.length} alerts from GDACS`);
      } else {
        console.warn('❌ Failed to fetch GDACS alerts:', gdacsAlerts.reason);
      }

      // Remove duplicates based on ID and sort by time (most recent first)
      const uniqueDisasters = allDisasters.filter((disaster, index, self) =>
        index === self.findIndex(d => d.id === disaster.id)
      );

      const sortedDisasters = uniqueDisasters
        .sort((a, b) => b.time.getTime() - a.time.getTime())
        .slice(0, 100); // Limit to 100 most recent events

      console.log(`🎯 Total unique disasters: ${sortedDisasters.length}`);
      return sortedDisasters;
    } catch (error) {
      console.error('Error fetching disaster data:', error);
      return [];
    }
  }

  /**
   * Get disaster statistics
   */
  async getDisasterStatistics(): Promise<{
    totalActive: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    lastUpdated: Date;
  }> {
    try {
      const disasters = await this.fetchAllRecentDisasters();
      
      const stats = disasters.reduce(
        (acc, disaster) => {
          acc.totalActive++;
          acc[disaster.severity]++;
          return acc;
        },
        { totalActive: 0, critical: 0, high: 0, medium: 0, low: 0 }
      );

      return {
        ...stats,
        lastUpdated: new Date(),
      };
    } catch (error) {
      console.error('Error getting disaster statistics:', error);
      return {
        totalActive: 0,
        critical: 0,
        high: 0,
        medium: 0,
        low: 0,
        lastUpdated: new Date(),
      };
    }
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const disasterDataService = DisasterDataService.getInstance();
